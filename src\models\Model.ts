import { Model as BaseModel } from 'vue-api-query';
import { TokenService } from '@/stores/auth-store';
import { getEnvVar } from '@/util/env';
import type { AxiosRequestConfig } from 'axios';

export default class Model extends BaseModel {
  baseURL() {
    return getEnvVar('VITE_APP_API_URL');
  }

  request(config: AxiosRequestConfig) {
    const token = TokenService.getAccessToken();

    if (!config.headers) {
      config.headers = {};
    }

    if (token) {
      config.headers!.Authorization = 'Bearer ' + token; // Set JWT token
    }
    return this.$http.request(config);
  }
}
