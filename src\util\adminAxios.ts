import createAuthRefreshInterceptor from '@antik-web/axios-auth-refresh-response-data';
import axios from 'axios';
import { Model } from 'vue-api-query';
import { getEnvVar } from './env';
import router from '@/router';
import { TokenService, useAuthStore } from '@/stores/auth-store';
import type { AxiosError } from 'axios';

const adminApi = axios.create({
  baseURL: getEnvVar('VITE_APP_API_URL') as string,
  timeout: 30000, // Request timeout
});

adminApi.interceptors.request.use(
  config => {
    const token = TokenService.getAccessToken();
    const lang = localStorage.getItem('appLang');

    if (token) {
      config.headers.Authorization = 'Bearer ' + token; // Set JWT token
    }

    if (lang) {
      config.headers['Accept-Language'] = lang;
    }

    if (config.method?.toLowerCase() === 'get') {
      const params = new URLSearchParams(config.params || {});
      params.append('translatable', '1');
      config.params = params;
    }

    return config;
  },
  error => {
    // Do something with request error
    console.error(error); // For debug
    void Promise.reject(error);
  },
);

const refreshAuthLogic = async(failedRequest: AxiosError): Promise<void> => {
  await handleTokenAuth(failedRequest);
};

const refreshTokens = async() => {
  const { promptRefreshToken } = useAuthStore();
  const { accessToken, refreshToken } = await promptRefreshToken();
  return { accessToken, refreshToken };
};

export const handleTokenAuth = async(res: AxiosError) => {
  try {
    if (router.currentRoute.value.name === 'login') {
      return;
    }

    const { accessToken } = await refreshTokens();
    res.response!.config.headers.Authorization = 'Bearer ' + accessToken;
    await Promise.resolve();
  } catch (err: any) {
    // TODO
  }
};

createAuthRefreshInterceptor(adminApi, refreshAuthLogic, { pauseInstanceWhileRefreshing: true });

adminApi.interceptors.response.use(
  (res) => res,
  async(error: AxiosError) => {
    if (error?.response?.status === 429 && (error.request as XMLHttpRequest).responseURL.search(/ping|token/) === -1) {
      // TODO
    }

    return Promise.reject(error);
  },
);

export default adminApi;

Model.$http = axios;
