import { IconCashRegister, IconUsersGroup } from '@tabler/icons-vue';
import { BaggageClaim, Boxes, Home, Lock, Settings, UserRoundCog, Wrench } from 'lucide-vue-next';
import TurnstileIcon from '@/assets/svg/turnstile.svg';
import { routeMap } from '@/router/routes';

export const navLinks = {
  top: [
    {
      name: routeMap.home.name,
      icon: Home,
      i18nTitle: routeMap.home.meta.i18nTitle,
      activeIn: [routeMap.home.name],
    },
    {
      icon: IconCashRegister,
      i18nTitle: routeMap.cr.meta.i18nTitle,
      permission: 'cash register admin',
      children: [
        {
          name: routeMap.cr.children.list.name,
          i18nTitle: routeMap.cr.children.list.meta.i18nTitle,
          activeIn: [routeMap.cr.children.list.name, routeMap.cr.children.editor.name],
        },
        {
          name: routeMap.cr.children.groups.name,
          i18nTitle: routeMap.cr.children.groups.meta.i18nTitle,
          activeIn: [routeMap.cr.children.groups.name],
        },
      ],
    },
    {
      icon: Boxes,
      i18nTitle: routeMap.products.meta.i18nTitle,
      permission: 'product view',
      children: [
        {
          name: routeMap.products.children.list.name,
          i18nTitle: routeMap.products.children.list.meta.i18nTitle,
          activeIn: [routeMap.products.children.list.name, routeMap.products.children.create.name, routeMap.products.children.edit.name],
        },
        {
          name: routeMap.products.children.categories.name,
          i18nTitle: routeMap.products.children.categories.meta.i18nTitle,
          activeIn: [routeMap.products.children.categories.name],
        },
        {
          name: routeMap.products.children.families.name,
          i18nTitle: routeMap.products.children.families.meta.i18nTitle,
          activeIn: [routeMap.products.children.families.name, routeMap.products.children.familyCreate.name, routeMap.products.children.familyEdit.name],
        },
      ],
    },
    {
      icon: TurnstileIcon,
      i18nTitle: routeMap.turnstiles.meta.i18nTitle,
      permission: 'turnstile view',
      children: [
        {
          name: routeMap.turnstiles.children.list.name,
          i18nTitle: routeMap.turnstiles.children.list.meta.i18nTitle,
          activeIn: [routeMap.turnstiles.children.list.name],
        },
        {
          name: routeMap.turnstiles.children.categories.name,
          i18nTitle: routeMap.turnstiles.children.categories.meta.i18nTitle,
          activeIn: [routeMap.turnstiles.children.categories.name],
        },
      ],
    },
    {
      icon: Wrench,
      i18nTitle: routeMap.services.meta.i18nTitle,
      permission: 'reservation view',
      children: [
        {
          name: routeMap.services.children.list.name,
          i18nTitle: routeMap.services.children.list.meta.i18nTitle,
          activeIn: [routeMap.services.children.list.name, routeMap.services.children.create.name, routeMap.services.children.edit.name],
        },
      ],
    },
    {
      name: routeMap.customers.name,
      icon: IconUsersGroup,
      i18nTitle: routeMap.customers.meta.i18nTitle,
      permission: 'customer view',
      children: [
        {
          name: routeMap.customers.children.list.name,
          i18nTitle: routeMap.customers.children.list.meta.i18nTitle,
          activeIn: [routeMap.customers.children.list.name, routeMap.customers.children.create.name, routeMap.customers.children.edit.name],
        },
        {
          name: routeMap.customers.children.groups.name,
          i18nTitle: routeMap.customers.children.groups.meta.i18nTitle,
          activeIn: [routeMap.customers.children.groups.name],
        },
      ],
    },
    {
      name: routeMap.orders.children.list.name,
      icon: BaggageClaim,
      i18nTitle: routeMap.orders.meta.i18nTitle,
      permission: 'order view',
      activeIn: [routeMap.orders.children.list.name, routeMap.orders.children.create.name, routeMap.orders.children.edit.name],
    },
  ],
  bottom: [
    {
      name: routeMap.management.children.users.name,
      icon: UserRoundCog,
      i18nTitle: routeMap.management.children.users.meta.i18nTitle,
      permission: 'manage user',
    },
    {
      name: routeMap.management.children.roles.name,
      icon: Lock,
      i18nTitle: routeMap.management.children.roles.meta.i18nTitle,
      permission: 'manage role',
    },
    {
      name: routeMap.settings.name,
      icon: Settings,
      i18nTitle: routeMap.settings.meta.i18nTitle,
      permission: 'manage settings',
    },
  ],
};

export const filterNavLinks = (permissions: string[]) => {
  return {
    top: navLinks.top.filter((link) => {
      if (!link.permission) {
        return true;
      }
      return permissions.includes(link.permission);
    }),
    bottom: navLinks.bottom.filter((link) => {
      if (!link.permission) {
        return true;
      }
      return permissions.includes(link.permission);
    }),
  };
};
