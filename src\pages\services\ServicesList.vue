<script setup lang="ts">
import { IconCircleCheckFilled, IconCircleXFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { PlusIcon, Trash2, Edit, Search } from 'lucide-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { routeMap } from '@/router/routes';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { formatDate } from '@/util/datetime';
import { getFromMultiLangObject } from '@/util/multilang';
import { deployToast, ToastType } from '@/util/toast';
import type { Meta, Response, Service } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const { t } = useI18n();
const authStore = useAuthStore();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const componentState = ref(ComponentStateType.LOADING);
const services = ref<Service[]>([]);
const paginationMeta = ref<Meta>();

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<Service[]>>('/api/admin/reservations/services', { params });

    services.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);

const removeService = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/reservations/services/${id}`);
    await fetchData();
    deployToast(ToastType.SUCCESS, {
      text: t('services.service-deleted'),
      timeout: 6000,
    });
  } catch {
    componentState.value = ComponentStateType.ERROR;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <router-link :class="{ 'pointer-events-none select-none opacity-50': !authStore.hasPermission('reservation manage') }" :to="{ name: routeMap.services.children.create.name }" class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer">
          <PlusIcon class="aspect-square text-gray-500" />
          <div class="text-sm font-medium text-gray-600">{{ $t('services.new-service') }}</div>
        </router-link>
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="text-center">#</TableHead>
            <TableHead>{{ $t('services.name') }}</TableHead>
            <TableHead>{{ $t('services.description') }}</TableHead>
            <TableHead>{{ $t('services.capacity') }}</TableHead>
            <TableHead>{{ $t('services.duration') }}</TableHead>
            <TableHead>{{ $t('misc.created') }}</TableHead>
            <TableHead>{{ $t('services.active') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(service, idx) in services" :key="idx">
            <TableCell class="!py-0 w-6 text-center text-gray-500">
              <div>{{ idx + 1 }}.</div>
            </TableCell>

            <TableCell v-if="service.name">
              <router-link :to="{ name: routeMap.services.children.edit.name, params: { id: service.id } }">
                <div class="hover:underline">{{ getFromMultiLangObject(service.name).value }}</div>
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="service.description">
              <div class="text-sm text-gray-600 max-w-xs truncate">{{ getFromMultiLangObject(service.description).value }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="service.capacity">
              <div class="">{{ service.capacity }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="service.duration_minutes">
              <div class="">{{ service.duration_minutes }} min</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="service.created_at">
              <div>{{ formatDate(service.created_at) }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="service.active" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <router-link :to="{ name: routeMap.services.children.edit.name, params: { id: service.id } }" class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90">
                  <Edit class="w-full h-full" />
                </router-link>
              </div>

              <AlertDialog>
                <AlertDialogTrigger :disabled="!authStore.hasPermission('reservation manage')" @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('services.delete-service-confirmation', { name: getFromMultiLangObject(service.name).value }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeService(service.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="services?.length" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="services?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
