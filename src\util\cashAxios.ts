import createAuthRefreshInterceptor from '@antik-web/axios-auth-refresh-response-data';
import axios from 'axios';
import { getEnvVar } from './env';
import router from '@/router';
import { TokenService, useCashAuthStore } from '@/stores/cash-auth-store';
import type { AxiosError } from 'axios';

let deviceId: string | null = null;
let frontendId: string | null = null;

export const setDeviceIdHeader = (id: string) => {
  deviceId = id;
};

export const setFrontendIdHeader = (id: string) => {
  frontendId = id;
};

const cashApi = axios.create({
  baseURL: getEnvVar('VITE_APP_API_URL') as string,
  timeout: 30000, // Request timeout
});

cashApi.interceptors.request.use(
  config => {
    const token = TokenService.getAccessToken();
    const lang = localStorage.getItem('appLang');

    if (token) {
      config.headers.Authorization = 'Bearer ' + token; // Set JWT token
    }

    if (deviceId) {
      config.headers['Device-Id'] = deviceId;
    } else if (getEnvVar('DEV')) {
      config.headers['Device-Id'] = '11b160ef-1ca1-4503-afff-21f904b9602f';
    }

    if (frontendId) {
      config.headers['Frontend-Id'] = frontendId;
    } else if (getEnvVar('DEV')) {
      config.headers['Frontend-Id'] = '01k092kegdrxj6cekm46zrpqr9';
    }

    if (lang) {
      config.headers['Accept-Language'] = lang;
    }

    if (config.method?.toLowerCase() === 'get') {
      const params = new URLSearchParams(config.params || {});
      params.append('translatable', '1');
      config.params = params;
    }

    return config;
  },
  error => {
    // Do something with request error
    console.error(error); // For debug
    void Promise.reject(error);
  },
);

const refreshAuthLogic = async(failedRequest: AxiosError): Promise<void> => {
  await handleTokenAuth(failedRequest);
};

const refreshTokens = async() => {
  const { promptRefreshToken } = useCashAuthStore();
  const { accessToken, refreshToken } = await promptRefreshToken();
  return { accessToken, refreshToken };
};

export const handleTokenAuth = async(res: AxiosError) => {
  try {
    if (router.currentRoute.value.name === 'login') {
      return;
    }

    const { accessToken } = await refreshTokens();
        res.response!.config.headers.Authorization = 'Bearer ' + accessToken;
        await Promise.resolve();
  } catch (err: any) {
    // TODO
  }
};

createAuthRefreshInterceptor(cashApi, refreshAuthLogic, { pauseInstanceWhileRefreshing: true });

cashApi.interceptors.response.use(
  (res) => res,
  async(error: AxiosError) => {
    if (error?.response?.status === 429 && (error.request as XMLHttpRequest).responseURL.search(/ping|token/) === -1) {
      // TODO
    }

    return Promise.reject(error);
  },
);

export default cashApi;
